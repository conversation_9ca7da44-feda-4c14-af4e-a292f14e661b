import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:provider/provider.dart';
import 'package:voice_assistant/services/real_object_detection_service.dart';
import 'package:voice_assistant/services/theme_service.dart';
import 'dart:math' as math;

/// Widget caméra pour la détection d'objets en temps réel avec TensorFlow Lite
class RealObjectDetectionCamera extends StatefulWidget {
  final List<CameraDescription> cameras;
  final String model;

  const RealObjectDetectionCamera({
    super.key,
    required this.cameras,
    required this.model,
  });

  @override
  State<RealObjectDetectionCamera> createState() => _RealObjectDetectionCameraState();
}

class _RealObjectDetectionCameraState extends State<RealObjectDetectionCamera> {
  CameraController? _controller;
  RealObjectDetectionService? _detectionService;
  bool _isDetecting = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
    _detectionService = RealObjectDetectionService();
  }

  /// Initialise la caméra
  Future<void> _initializeCamera() async {
    if (widget.cameras.isEmpty) {
      setState(() {
        _errorMessage = 'Aucune caméra disponible';
      });
      return;
    }

    try {
      _controller = CameraController(
        widget.cameras.first,
        ResolutionPreset.high,
        enableAudio: false,
      );

      await _controller!.initialize();
      
      if (!mounted) return;

      setState(() {});

      // Initialiser le service de détection
      await _detectionService!.initialize();
      await _detectionService!.changeModel(widget.model);

      // Démarrer le flux d'images pour la détection
      await _startImageStream();
      
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur d\'initialisation de la caméra: $e';
      });
    }
  }

  /// Démarre le flux d'images pour la détection
  Future<void> _startImageStream() async {
    if (_controller == null || !_controller!.value.isInitialized) return;

    // Démarrer le flux d'images
    await _controller!.startImageStream((CameraImage image) {
      if (!_isDetecting && mounted) {
        _processImage(image);
      }
    });
  }

  /// Traite une image pour la détection d'objets
  Future<void> _processImage(CameraImage image) async {
    if (_isDetecting || _detectionService == null) return;

    _isDetecting = true;

    try {
      // Détecter les objets dans l'image
      await _detectionService!.detectObjects(image);
      
    } catch (e) {
      debugPrint('Erreur lors du traitement de l\'image: $e');
    } finally {
      _isDetecting = false;
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeService = Provider.of<ThemeService>(context);
    final theme = themeService.getThemeData(context);

    // Afficher l'erreur si présente
    if (_errorMessage != null) {
      return _buildErrorWidget(theme);
    }

    // Afficher le chargement si la caméra n'est pas initialisée
    if (_controller == null || !_controller!.value.isInitialized) {
      return _buildLoadingWidget(theme);
    }

    return _buildCameraPreview(theme);
  }

  /// Construit le widget d'erreur
  Widget _buildErrorWidget(ThemeData theme) {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: theme.colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Erreur de caméra',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.error,
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _errorMessage!,
                style: const TextStyle(color: Colors.white),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _errorMessage = null;
                });
                _initializeCamera();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Réessayer'),
            ),
          ],
        ),
      ),
    );
  }

  /// Construit le widget de chargement
  Widget _buildLoadingWidget(ThemeData theme) {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: theme.colorScheme.primary,
            ),
            const SizedBox(height: 16),
            const Text(
              'Initialisation de la caméra...',
              style: TextStyle(color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }

  /// Construit l'aperçu de la caméra
  Widget _buildCameraPreview(ThemeData theme) {
    final size = MediaQuery.of(context).size;
    final deviceRatio = size.width / size.height;

    return ClipRect(
      child: Transform.scale(
        scale: _controller!.value.aspectRatio / deviceRatio,
        child: Center(
          child: AspectRatio(
            aspectRatio: _controller!.value.aspectRatio,
            child: Stack(
              children: [
                CameraPreview(_controller!),
                _buildOverlay(theme),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Construit l'overlay avec les informations de détection
  Widget _buildOverlay(ThemeData theme) {
    return Consumer<RealObjectDetectionService>(
      builder: (context, detectionService, child) {
        return Stack(
          children: [
            // Indicateur d'état en haut à gauche
            Positioned(
              top: 16,
              left: 16,
              child: _buildStatusIndicator(theme, detectionService),
            ),
            
            // Boîtes de détection
            if (detectionService.recognitions != null)
              ...detectionService.recognitions!.map((recognition) {
                return _buildDetectionBox(theme, recognition);
              }),
          ],
        );
      },
    );
  }

  /// Construit l'indicateur d'état
  Widget _buildStatusIndicator(ThemeData theme, RealObjectDetectionService service) {
    Color indicatorColor;
    IconData indicatorIcon;
    String statusText;

    if (!service.isModelLoaded) {
      indicatorColor = Colors.blue;
      indicatorIcon = Icons.hourglass_empty;
      statusText = 'Chargement...';
    } else if (service.isDetecting) {
      indicatorColor = Colors.orange;
      indicatorIcon = Icons.search;
      statusText = 'Détection...';
    } else {
      indicatorColor = Colors.green;
      indicatorIcon = Icons.check_circle;
      statusText = 'Actif';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            indicatorIcon,
            color: indicatorColor,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            statusText,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  /// Construit une boîte de détection
  Widget _buildDetectionBox(ThemeData theme, dynamic recognition) {
    // Pour les modèles avec boîtes de détection (YOLO, SSD)
    if (recognition is Map && recognition.containsKey('rect')) {
      final rect = recognition['rect'];
      final className = recognition['detectedClass'] ?? 'Objet';
      final confidence = recognition['confidenceInClass'] ?? 0.0;
      
      return Positioned(
        left: rect['x'] * MediaQuery.of(context).size.width,
        top: rect['y'] * MediaQuery.of(context).size.height,
        width: rect['w'] * MediaQuery.of(context).size.width,
        height: rect['h'] * MediaQuery.of(context).size.height,
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: theme.colorScheme.primary,
              width: 2,
            ),
          ),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
            color: theme.colorScheme.primary,
            child: Text(
              '$className ${(confidence * 100).toInt()}%',
              style: TextStyle(
                color: theme.colorScheme.onPrimary,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      );
    }
    
    return const SizedBox.shrink();
  }
}

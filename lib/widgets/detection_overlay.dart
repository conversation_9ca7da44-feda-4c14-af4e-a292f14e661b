import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:voice_assistant/services/object_detection_service.dart';
import 'package:voice_assistant/services/theme_service.dart';
import 'dart:math' as math;

/// Widget d'overlay pour afficher les résultats de détection d'objets
class DetectionOverlay extends StatelessWidget {
  final List<DetectionResult> results;
  final int previewHeight;
  final int previewWidth;
  final double screenHeight;
  final double screenWidth;
  final DetectionModel model;

  const DetectionOverlay({
    super.key,
    required this.results,
    required this.previewHeight,
    required this.previewWidth,
    required this.screenHeight,
    required this.screenWidth,
    required this.model,
  });

  @override
  Widget build(BuildContext context) {
    final themeService = Provider.of<ThemeService>(context);
    final theme = themeService.getThemeData(context);

    return Stack(
      children: _buildDetectionWidgets(theme),
    );
  }

  /// Construit les widgets de détection selon le type de modèle
  List<Widget> _buildDetectionWidgets(ThemeData theme) {
    switch (model) {
      case DetectionModel.mobileNet:
        return _buildClassificationResults(theme);
      case DetectionModel.poseNet:
        return _buildPoseResults(theme);
      case DetectionModel.ssdMobileNet:
      case DetectionModel.yoloTiny:
      default:
        return _buildBoundingBoxes(theme);
    }
  }

  /// Construit les boîtes de détection pour les modèles d'object detection
  List<Widget> _buildBoundingBoxes(ThemeData theme) {
    return results.map((result) {
      if (result.rect == null) return const SizedBox.shrink();

      final rect = result.rect!;
      final x = rect['x'] ?? 0.0;
      final y = rect['y'] ?? 0.0;
      final w = rect['w'] ?? 0.0;
      final h = rect['h'] ?? 0.0;

      // Calculer les coordonnées d'affichage
      final displayCoords = _calculateDisplayCoordinates(x, y, w, h);

      return Positioned(
        left: math.max(0, displayCoords['x']!),
        top: math.max(0, displayCoords['y']!),
        width: displayCoords['w'],
        height: displayCoords['h'],
        child: _buildBoundingBox(theme, result),
      );
    }).toList();
  }

  /// Construit les résultats de classification pour MobileNet
  List<Widget> _buildClassificationResults(ThemeData theme) {
    double offset = 20;
    return results.map((result) {
      offset += 30;
      return Positioned(
        left: 16,
        top: offset,
        child: _buildClassificationLabel(theme, result),
      );
    }).toList();
  }

  /// Construit les points clés pour PoseNet
  List<Widget> _buildPoseResults(ThemeData theme) {
    final widgets = <Widget>[];
    
    for (final result in results) {
      if (result.keypoints != null) {
        final keypoints = result.keypoints!;
        for (final entry in keypoints.entries) {
          final point = entry.value as Map<String, dynamic>;
          final x = point['x'] as double? ?? 0.0;
          final y = point['y'] as double? ?? 0.0;
          final part = entry.key;

          final displayCoords = _calculateKeypointCoordinates(x, y);
          
          widgets.add(
            Positioned(
              left: displayCoords['x']! - 6,
              top: displayCoords['y']! - 6,
              child: _buildKeypoint(theme, part),
            ),
          );
        }
      }
    }
    
    return widgets;
  }

  /// Calcule les coordonnées d'affichage pour les boîtes de détection
  Map<String, double> _calculateDisplayCoordinates(double x, double y, double w, double h) {
    double scaleW, scaleH, displayX, displayY, displayW, displayH;

    if (screenHeight / screenWidth > previewHeight / previewWidth) {
      scaleW = screenHeight / previewHeight * previewWidth;
      scaleH = screenHeight;
      final difW = (scaleW - screenWidth) / scaleW;
      displayX = (x - difW / 2) * scaleW;
      displayW = w * scaleW;
      if (x < difW / 2) displayW -= (difW / 2 - x) * scaleW;
      displayY = y * scaleH;
      displayH = h * scaleH;
    } else {
      scaleH = screenWidth / previewWidth * previewHeight;
      scaleW = screenWidth;
      final difH = (scaleH - screenHeight) / scaleH;
      displayX = x * scaleW;
      displayW = w * scaleW;
      displayY = (y - difH / 2) * scaleH;
      displayH = h * scaleH;
      if (y < difH / 2) displayH -= (difH / 2 - y) * scaleH;
    }

    return {
      'x': displayX,
      'y': displayY,
      'w': displayW,
      'h': displayH,
    };
  }

  /// Calcule les coordonnées d'affichage pour les points clés
  Map<String, double> _calculateKeypointCoordinates(double x, double y) {
    double scaleW, scaleH, displayX, displayY;

    if (screenHeight / screenWidth > previewHeight / previewWidth) {
      scaleW = screenHeight / previewHeight * previewWidth;
      scaleH = screenHeight;
      final difW = (scaleW - screenWidth) / scaleW;
      displayX = (x - difW / 2) * scaleW;
      displayY = y * scaleH;
    } else {
      scaleH = screenWidth / previewWidth * previewHeight;
      scaleW = screenWidth;
      final difH = (scaleH - screenHeight) / scaleH;
      displayX = x * scaleW;
      displayY = (y - difH / 2) * scaleH;
    }

    return {
      'x': displayX,
      'y': displayY,
    };
  }

  /// Construit une boîte de détection
  Widget _buildBoundingBox(ThemeData theme, DetectionResult result) {
    final primaryColor = theme.colorScheme.primary;
    
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: primaryColor,
          width: 3.0,
        ),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: primaryColor.withValues(alpha: 0.9),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(4),
            topRight: Radius.circular(4),
          ),
        ),
        child: Text(
          '${result.detectedClass} ${(result.confidence * 100).toStringAsFixed(0)}%',
          style: TextStyle(
            color: theme.colorScheme.onPrimary,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// Construit un label de classification
  Widget _buildClassificationLabel(ThemeData theme, DetectionResult result) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        '${result.label ?? result.detectedClass} ${(result.confidence * 100).toStringAsFixed(0)}%',
        style: TextStyle(
          color: theme.colorScheme.onPrimary,
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// Construit un point clé pour PoseNet
  Widget _buildKeypoint(ThemeData theme, String part) {
    return Container(
      width: 12,
      height: 12,
      decoration: BoxDecoration(
        color: theme.colorScheme.primary,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Center(
        child: Container(
          width: 6,
          height: 6,
          decoration: BoxDecoration(
            color: theme.colorScheme.onPrimary,
            shape: BoxShape.circle,
          ),
        ),
      ),
    );
  }
}

/// Widget d'informations de détection flottant
class DetectionInfoPanel extends StatelessWidget {
  final List<DetectionResult> results;
  final DetectionModel model;
  final VoidCallback? onToggleVoice;
  final bool voiceEnabled;

  const DetectionInfoPanel({
    super.key,
    required this.results,
    required this.model,
    this.onToggleVoice,
    this.voiceEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final themeService = Provider.of<ThemeService>(context);
    final theme = themeService.getThemeData(context);

    return Positioned(
      top: 60,
      right: 16,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 200),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface.withValues(alpha: 0.95),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // En-tête avec modèle et contrôles
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  model.displayName,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (onToggleVoice != null)
                  GestureDetector(
                    onTap: onToggleVoice,
                    child: Icon(
                      voiceEnabled ? Icons.volume_up : Icons.volume_off,
                      size: 20,
                      color: voiceEnabled 
                          ? theme.colorScheme.primary 
                          : theme.colorScheme.outline,
                    ),
                  ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            // Résultats de détection
            if (results.isEmpty)
              Text(
                'Aucun objet détecté',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.outline,
                ),
              )
            else
              ...results.take(5).map((result) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: _getConfidenceColor(result.confidence, theme),
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '${result.detectedClass} (${(result.confidence * 100).toInt()}%)',
                        style: theme.textTheme.bodySmall,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              )),
          ],
        ),
      ),
    );
  }

  /// Retourne une couleur basée sur le niveau de confiance
  Color _getConfidenceColor(double confidence, ThemeData theme) {
    if (confidence > 0.8) return Colors.green;
    if (confidence > 0.6) return Colors.orange;
    if (confidence > 0.4) return theme.colorScheme.primary;
    return theme.colorScheme.outline;
  }
}

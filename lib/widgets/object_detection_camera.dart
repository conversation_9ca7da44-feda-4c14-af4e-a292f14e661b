import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:provider/provider.dart';
import 'package:voice_assistant/services/object_detection_service.dart';
import 'package:voice_assistant/services/theme_service.dart';
import 'dart:math' as math;

/// Callback pour les résultats de détection
typedef DetectionCallback = void Function(List<DetectionResult> results, int imageHeight, int imageWidth);

/// Widget caméra pour la détection d'objets en temps réel
class ObjectDetectionCamera extends StatefulWidget {
  final List<CameraDescription> cameras;
  final DetectionCallback? onDetection;
  final DetectionModel model;

  const ObjectDetectionCamera({
    super.key,
    required this.cameras,
    this.onDetection,
    this.model = DetectionModel.ssdMobileNet,
  });

  @override
  State<ObjectDetectionCamera> createState() => _ObjectDetectionCameraState();
}

class _ObjectDetectionCameraState extends State<ObjectDetectionCamera> {
  CameraController? _controller;
  ObjectDetectionService? _detectionService;
  bool _isDetecting = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
    _detectionService = ObjectDetectionService();
  }

  /// Initialise la caméra
  Future<void> _initializeCamera() async {
    if (widget.cameras.isEmpty) {
      setState(() {
        _errorMessage = 'Aucune caméra disponible';
      });
      return;
    }

    try {
      _controller = CameraController(
        widget.cameras.first,
        ResolutionPreset.high,
        enableAudio: false,
      );

      await _controller!.initialize();
      
      if (!mounted) return;

      setState(() {});

      // Démarrer le flux d'images pour la détection
      await _startImageStream();
      
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur d\'initialisation de la caméra: $e';
      });
    }
  }

  /// Démarre le flux d'images pour la détection
  Future<void> _startImageStream() async {
    if (_controller == null || !_controller!.value.isInitialized) return;

    // Initialiser le service de détection
    await _detectionService!.initialize();
    await _detectionService!.changeModel(widget.model);

    // Démarrer le flux d'images
    await _controller!.startImageStream((CameraImage image) {
      if (!_isDetecting && mounted) {
        _processImage(image);
      }
    });
  }

  /// Traite une image pour la détection d'objets
  Future<void> _processImage(CameraImage image) async {
    if (_isDetecting || _detectionService == null) return;

    _isDetecting = true;

    try {
      // Détecter les objets dans l'image
      await _detectionService!.detectObjects(image);
      
      // Notifier les résultats via le callback
      if (widget.onDetection != null) {
        widget.onDetection!(
          _detectionService!.lastResults,
          image.height,
          image.width,
        );
      }
      
    } catch (e) {
      debugPrint('Erreur lors du traitement de l\'image: $e');
    } finally {
      _isDetecting = false;
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeService = Provider.of<ThemeService>(context);
    final theme = themeService.getThemeData(context);

    // Afficher l'erreur si présente
    if (_errorMessage != null) {
      return _buildErrorWidget(theme);
    }

    // Afficher le chargement si la caméra n'est pas initialisée
    if (_controller == null || !_controller!.value.isInitialized) {
      return _buildLoadingWidget(theme);
    }

    return _buildCameraPreview(theme);
  }

  /// Construit le widget d'erreur
  Widget _buildErrorWidget(ThemeData theme) {
    return Container(
      color: theme.scaffoldBackgroundColor,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: theme.colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Erreur de caméra',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.error,
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _errorMessage!,
                style: theme.textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _errorMessage = null;
                });
                _initializeCamera();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Réessayer'),
            ),
          ],
        ),
      ),
    );
  }

  /// Construit le widget de chargement
  Widget _buildLoadingWidget(ThemeData theme) {
    return Container(
      color: theme.scaffoldBackgroundColor,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: theme.colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              'Initialisation de la caméra...',
              style: theme.textTheme.bodyLarge,
            ),
          ],
        ),
      ),
    );
  }

  /// Construit l'aperçu de la caméra
  Widget _buildCameraPreview(ThemeData theme) {
    final size = MediaQuery.of(context).size;
    final deviceRatio = size.width / size.height;

    return ClipRect(
      child: Transform.scale(
        scale: _controller!.value.aspectRatio / deviceRatio,
        child: Center(
          child: AspectRatio(
            aspectRatio: _controller!.value.aspectRatio,
            child: Stack(
              children: [
                CameraPreview(_controller!),
                _buildOverlay(theme),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Construit l'overlay avec les informations de détection
  Widget _buildOverlay(ThemeData theme) {
    return Consumer<ObjectDetectionService>(
      builder: (context, detectionService, child) {
        return Stack(
          children: [
            // Indicateur d'état en haut à gauche
            Positioned(
              top: 16,
              left: 16,
              child: _buildStatusIndicator(theme, detectionService),
            ),
            
            // Informations de détection en bas
            Positioned(
              bottom: 16,
              left: 16,
              right: 16,
              child: _buildDetectionInfo(theme, detectionService),
            ),
          ],
        );
      },
    );
  }

  /// Construit l'indicateur d'état
  Widget _buildStatusIndicator(ThemeData theme, ObjectDetectionService service) {
    Color indicatorColor;
    IconData indicatorIcon;
    String statusText;

    switch (service.state) {
      case DetectionState.idle:
        indicatorColor = Colors.green;
        indicatorIcon = Icons.check_circle;
        statusText = 'Prêt';
        break;
      case DetectionState.detecting:
        indicatorColor = Colors.orange;
        indicatorIcon = Icons.search;
        statusText = 'Détection...';
        break;
      case DetectionState.loading:
        indicatorColor = Colors.blue;
        indicatorIcon = Icons.hourglass_empty;
        statusText = 'Chargement...';
        break;
      case DetectionState.error:
        indicatorColor = Colors.red;
        indicatorIcon = Icons.error;
        statusText = 'Erreur';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            indicatorIcon,
            color: indicatorColor,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            statusText,
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// Construit les informations de détection
  Widget _buildDetectionInfo(ThemeData theme, ObjectDetectionService service) {
    if (service.lastResults.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          'Aucun objet détecté',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Objets détectés:',
            style: theme.textTheme.titleSmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ...service.lastResults.take(3).map((result) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Text(
              '• ${result.detectedClass} (${(result.confidence * 100).toInt()}%)',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.white,
              ),
            ),
          )),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:camera/camera.dart';
import 'package:voice_assistant/services/theme_service.dart';
import 'package:voice_assistant/services/tts_service.dart';
import 'package:voice_assistant/services/real_object_detection_service.dart';
import 'package:voice_assistant/widgets/real_object_detection_camera.dart';
import 'package:voice_assistant/models/detection_models.dart';

/// Page de reconnaissance d'objets en temps réel avec TensorFlow Lite
class RealObjectRecognitionPage extends StatefulWidget {
  const RealObjectRecognitionPage({super.key});

  @override
  State<RealObjectRecognitionPage> createState() => _RealObjectRecognitionPageState();
}

class _RealObjectRecognitionPageState extends State<RealObjectRecognitionPage> {
  final TtsService _ttsService = TtsService();
  RealObjectDetectionService? _detectionService;
  
  List<CameraDescription>? _cameras;
  String _selectedModel = ssd;
  bool _isInitializing = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  @override
  void dispose() {
    _detectionService?.dispose();
    super.dispose();
  }

  /// Initialise la caméra et le service de détection
  Future<void> _initializeCamera() async {
    try {
      // Initialiser les caméras
      _cameras = await availableCameras();
      
      if (_cameras == null || _cameras!.isEmpty) {
        throw Exception('Aucune caméra disponible');
      }

      // Initialiser le service de détection
      _detectionService = RealObjectDetectionService();
      
      setState(() {
        _isInitializing = false;
      });

      // Annoncer que la fonctionnalité est prête
      await _ttsService.speak(
        'Reconnaissance d\'objets activée. Dirigez la caméra vers les objets à identifier.',
      );
      
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur d\'initialisation: $e';
        _isInitializing = false;
      });
    }
  }

  /// Change le modèle de détection
  Future<void> _changeModel(String model) async {
    if (_detectionService != null && model != _selectedModel) {
      setState(() {
        _selectedModel = model;
      });
      await _detectionService!.changeModel(model);
      HapticFeedback.lightImpact();
    }
  }

  /// Active/désactive les annonces vocales
  void _toggleVoiceAnnouncements() {
    if (_detectionService != null) {
      final newState = !_detectionService!.voiceEnabled;
      _detectionService!.setVoiceEnabled(newState);
      HapticFeedback.lightImpact();
      _ttsService.speak(newState ? 'Annonces vocales activées' : 'Annonces vocales désactivées');
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeService = Provider.of<ThemeService>(context);
    final theme = themeService.getThemeData(context);

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text(
          'Reconnaissance d\'Objets',
          style: TextStyle(fontWeight: FontWeight.w600, fontSize: 20),
        ),
        backgroundColor: Colors.black.withValues(alpha: 0.8),
        elevation: 0,
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: const TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
        actions: [
          // Bouton de sélection de modèle
          PopupMenuButton<String>(
            icon: const Icon(Icons.model_training, color: Colors.white),
            onSelected: _changeModel,
            itemBuilder: (context) => [
              PopupMenuItem(
                value: ssd,
                child: Row(
                  children: [
                    Icon(
                      _selectedModel == ssd ? Icons.check : Icons.circle_outlined,
                      color: _selectedModel == ssd ? theme.colorScheme.primary : null,
                    ),
                    const SizedBox(width: 8),
                    const Text('SSD MobileNet'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: yolo,
                child: Row(
                  children: [
                    Icon(
                      _selectedModel == yolo ? Icons.check : Icons.circle_outlined,
                      color: _selectedModel == yolo ? theme.colorScheme.primary : null,
                    ),
                    const SizedBox(width: 8),
                    const Text('Tiny YOLOv2'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: mobilenet,
                child: Row(
                  children: [
                    Icon(
                      _selectedModel == mobilenet ? Icons.check : Icons.circle_outlined,
                      color: _selectedModel == mobilenet ? theme.colorScheme.primary : null,
                    ),
                    const SizedBox(width: 8),
                    const Text('MobileNet'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: posenet,
                child: Row(
                  children: [
                    Icon(
                      _selectedModel == posenet ? Icons.check : Icons.circle_outlined,
                      color: _selectedModel == posenet ? theme.colorScheme.primary : null,
                    ),
                    const SizedBox(width: 8),
                    const Text('PoseNet'),
                  ],
                ),
              ),
            ],
          ),
          // Bouton d'activation/désactivation des annonces vocales
          Consumer<RealObjectDetectionService>(
            builder: (context, service, child) {
              return IconButton(
                icon: Icon(
                  service.voiceEnabled ? Icons.volume_up : Icons.volume_off,
                  color: Colors.white,
                ),
                onPressed: _toggleVoiceAnnouncements,
              );
            },
          ),
        ],
      ),
      body: _buildBody(theme),
    );
  }

  /// Construit le corps de la page selon l'état
  Widget _buildBody(ThemeData theme) {
    if (_isInitializing) {
      return _buildLoadingView(theme);
    }
    
    if (_errorMessage != null) {
      return _buildErrorView(theme);
    }
    
    if (_cameras == null || _cameras!.isEmpty) {
      return _buildNoCameraView(theme);
    }
    
    return _buildCameraView(theme);
  }

  /// Vue de chargement
  Widget _buildLoadingView(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: theme.colorScheme.primary),
          const SizedBox(height: 16),
          const Text(
            'Initialisation de la reconnaissance d\'objets...',
            style: TextStyle(color: Colors.white),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Vue d'erreur
  Widget _buildErrorView(ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: theme.colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Erreur',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.error,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: const TextStyle(color: Colors.white),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _errorMessage = null;
                  _isInitializing = true;
                });
                _initializeCamera();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Réessayer'),
            ),
          ],
        ),
      ),
    );
  }

  /// Vue sans caméra
  Widget _buildNoCameraView(ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.camera_alt_outlined,
              size: 64,
              color: theme.colorScheme.outline,
            ),
            const SizedBox(height: 16),
            const Text(
              'Aucune caméra disponible',
              style: TextStyle(color: Colors.white, fontSize: 18),
            ),
            const SizedBox(height: 8),
            const Text(
              'Veuillez vérifier que votre appareil dispose d\'une caméra et que les permissions sont accordées.',
              style: TextStyle(color: Colors.white70),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Vue avec caméra active
  Widget _buildCameraView(ThemeData theme) {
    return ChangeNotifierProvider.value(
      value: _detectionService!,
      child: Stack(
        children: [
          // Widget caméra avec détection TensorFlow Lite
          RealObjectDetectionCamera(
            cameras: _cameras!,
            model: _selectedModel,
          ),
          
          // Panneau d'informations flottant
          _buildInfoPanel(theme),
        ],
      ),
    );
  }

  /// Construit le panneau d'informations
  Widget _buildInfoPanel(ThemeData theme) {
    return Positioned(
      top: 80,
      right: 16,
      child: Consumer<RealObjectDetectionService>(
        builder: (context, service, child) {
          return Container(
            constraints: const BoxConstraints(maxWidth: 200),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: theme.colorScheme.primary.withValues(alpha: 0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // En-tête
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _selectedModel,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                    Icon(
                      service.voiceEnabled ? Icons.volume_up : Icons.volume_off,
                      size: 16,
                      color: service.voiceEnabled ? theme.colorScheme.primary : Colors.grey,
                    ),
                  ],
                ),
                
                const SizedBox(height: 8),
                
                // État du modèle
                Text(
                  service.isModelLoaded ? 'Modèle chargé' : 'Chargement...',
                  style: TextStyle(
                    color: service.isModelLoaded ? Colors.green : Colors.orange,
                    fontSize: 10,
                  ),
                ),
                
                const SizedBox(height: 4),
                
                // Résultats de détection
                if (service.recognitions == null || service.recognitions!.isEmpty)
                  const Text(
                    'Recherche d\'objets...',
                    style: TextStyle(color: Colors.white70, fontSize: 11),
                  )
                else
                  ...service.recognitions!.take(5).map((recognition) {
                    final className = _getClassName(recognition);
                    final confidence = _getConfidence(recognition);
                    
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Row(
                        children: [
                          Container(
                            width: 6,
                            height: 6,
                            decoration: BoxDecoration(
                              color: _getConfidenceColor(confidence, theme),
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 6),
                          Expanded(
                            child: Text(
                              '$className (${(confidence * 100).toInt()}%)',
                              style: const TextStyle(color: Colors.white, fontSize: 10),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    );
                  }),
              ],
            ),
          );
        },
      ),
    );
  }

  /// Extrait le nom de classe d'un résultat de détection
  String _getClassName(dynamic result) {
    if (result is Map) {
      return result['detectedClass'] ?? result['label'] ?? 'objet';
    }
    return 'objet';
  }

  /// Extrait la confiance d'un résultat de détection
  double _getConfidence(dynamic result) {
    if (result is Map) {
      return (result['confidenceInClass'] ?? result['confidence'] ?? 0.0) as double;
    }
    return 0.0;
  }

  /// Retourne une couleur basée sur le niveau de confiance
  Color _getConfidenceColor(double confidence, ThemeData theme) {
    if (confidence > 0.8) return Colors.green;
    if (confidence > 0.6) return Colors.orange;
    if (confidence > 0.4) return theme.colorScheme.primary;
    return Colors.grey;
  }
}

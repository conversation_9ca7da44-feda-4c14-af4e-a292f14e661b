import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:camera/camera.dart';
import 'package:voice_assistant/services/theme_service.dart';
import 'package:voice_assistant/services/tts_service.dart';
import 'package:voice_assistant/services/translation_service.dart';
import 'package:voice_assistant/models/detection_models.dart';

class ObjectRecognitionPage extends StatefulWidget {
  const ObjectRecognitionPage({super.key});

  @override
  State<ObjectRecognitionPage> createState() => _ObjectRecognitionPageState();
}

class _ObjectRecognitionPageState extends State<ObjectRecognitionPage> {
  final TtsService _ttsService = TtsService();
  final TranslationService _translationService = TranslationService();
  List<CameraDescription>? _cameras;
  String _selectedModel = ssd;
  bool _isInitializing = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  /// Initialise la caméra
  Future<void> _initializeCamera() async {
    try {
      // Initialiser les caméras
      _cameras = await availableCameras();

      setState(() {
        _isInitializing = false;
      });

      // Annoncer que la fonctionnalité est prête
      await _ttsService.speak(
        'Reconnaissance d\'objets activée. Cette fonctionnalité sera bientôt disponible avec détection en temps réel.',
      );
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur d\'initialisation: $e';
        _isInitializing = false;
      });
    }
  }

  /// Change le modèle de détection
  Future<void> _changeModel(String model) async {
    if (model != _selectedModel) {
      setState(() {
        _selectedModel = model;
      });
      HapticFeedback.lightImpact();
      await _ttsService.speak('Modèle $model sélectionné');
    }
  }

  /// Démonstration des annonces vocales
  Future<void> _demonstrateVoiceAnnouncement() async {
    HapticFeedback.mediumImpact();

    // Exemples d'objets détectés
    final examples = [
      'Je vois une personne avec 95% de certitude',
      'Je vois une chaise avec 87% de certitude',
      'Je vois un téléphone portable avec 92% de certitude',
      'Je vois plusieurs objets: une table, une tasse et un livre',
    ];

    final randomExample =
        examples[DateTime.now().millisecond % examples.length];
    await _ttsService.speak(randomExample);
  }

  @override
  Widget build(BuildContext context) {
    final themeService = Provider.of<ThemeService>(context);
    final theme = themeService.getThemeData(context);

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text(
          'Reconnaissance d\'Objets',
          style: TextStyle(fontWeight: FontWeight.w600, fontSize: 20),
        ),
        backgroundColor: Colors.black.withValues(alpha: 0.8),
        elevation: 0,
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: const TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
        actions: [
          // Bouton de sélection de modèle
          PopupMenuButton<DetectionModel>(
            icon: const Icon(Icons.model_training, color: Colors.white),
            onSelected: _changeModel,
            itemBuilder:
                (context) =>
                    DetectionModel.values.map((model) {
                      return PopupMenuItem(
                        value: model,
                        child: Row(
                          children: [
                            Icon(
                              _selectedModel == model
                                  ? Icons.check
                                  : Icons.circle_outlined,
                              color:
                                  _selectedModel == model
                                      ? theme.colorScheme.primary
                                      : null,
                            ),
                            const SizedBox(width: 8),
                            Text(model.displayName),
                          ],
                        ),
                      );
                    }).toList(),
          ),
          // Bouton d'activation/désactivation des annonces vocales
          Consumer<ObjectDetectionService>(
            builder: (context, service, child) {
              return IconButton(
                icon: Icon(
                  service.voiceAnnouncementEnabled
                      ? Icons.volume_up
                      : Icons.volume_off,
                  color: Colors.white,
                ),
                onPressed: _toggleVoiceAnnouncements,
              );
            },
          ),
        ],
      ),
      body: _buildBody(theme),
    );
  }

  /// Construit le corps de la page selon l'état
  Widget _buildBody(ThemeData theme) {
    if (_isInitializing) {
      return _buildLoadingView(theme);
    }

    if (_errorMessage != null) {
      return _buildErrorView(theme);
    }

    if (_cameras == null || _cameras!.isEmpty) {
      return _buildNoCameraView(theme);
    }

    return _buildCameraView(theme);
  }

  /// Vue de chargement
  Widget _buildLoadingView(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: theme.colorScheme.primary),
          const SizedBox(height: 16),
          Text(
            'Initialisation de la reconnaissance d\'objets...',
            style: theme.textTheme.bodyLarge?.copyWith(color: Colors.white),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Vue d'erreur
  Widget _buildErrorView(ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: theme.colorScheme.error),
            const SizedBox(height: 16),
            Text(
              'Erreur',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.error,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: theme.textTheme.bodyMedium?.copyWith(color: Colors.white),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _errorMessage = null;
                  _isInitializing = true;
                });
                _initializeServices();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Réessayer'),
            ),
          ],
        ),
      ),
    );
  }

  /// Vue sans caméra
  Widget _buildNoCameraView(ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.camera_alt_outlined,
              size: 64,
              color: theme.colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              'Aucune caméra disponible',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Veuillez vérifier que votre appareil dispose d\'une caméra et que les permissions sont accordées.',
              style: theme.textTheme.bodyMedium?.copyWith(color: Colors.white),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Vue avec caméra active
  Widget _buildCameraView(ThemeData theme) {
    return Stack(
      children: [
        // Caméra
        ObjectDetectionCamera(
          cameras: _cameras!,
          model: _selectedModel,
          onDetection: _onDetection,
        ),

        // Overlay de détection
        DetectionOverlay(
          results: _detectionResults,
          previewHeight: _imageHeight,
          previewWidth: _imageWidth,
          screenHeight: MediaQuery.of(context).size.height,
          screenWidth: MediaQuery.of(context).size.width,
          model: _selectedModel,
        ),

        // Panneau d'informations
        DetectionInfoPanel(
          results: _detectionResults,
          model: _selectedModel,
          voiceEnabled: _detectionService?.voiceAnnouncementEnabled ?? true,
          onToggleVoice: _toggleVoiceAnnouncements,
        ),
      ],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:camera/camera.dart';
import 'package:voice_assistant/services/theme_service.dart';
import 'package:voice_assistant/services/tts_service.dart';
import 'package:voice_assistant/services/translation_service.dart';
import 'package:voice_assistant/models/detection_models.dart';
import 'dart:math' as math;
import 'dart:async';

/// Page de reconnaissance d'objets en temps réel
class ObjectRecognitionPage extends StatefulWidget {
  const ObjectRecognitionPage({super.key});

  @override
  State<ObjectRecognitionPage> createState() => _ObjectRecognitionPageState();
}

class _ObjectRecognitionPageState extends State<ObjectRecognitionPage> {
  final TtsService _ttsService = TtsService();
  final TranslationService _translationService = TranslationService();

  List<CameraDescription>? _cameras;
  CameraController? _controller;
  String _selectedModel = ssd;
  List<dynamic>? _recognitions;
  int _imageHeight = 0;
  int _imageWidth = 0;
  bool _isDetecting = false;
  bool _voiceEnabled = true;
  DateTime? _lastAnnouncementTime;

  bool _isInitializing = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  /// Initialise la caméra et le modèle
  Future<void> _initializeCamera() async {
    try {
      // Initialiser les caméras
      _cameras = await availableCameras();

      if (_cameras == null || _cameras!.isEmpty) {
        throw Exception('Aucune caméra disponible');
      }

      // Initialiser le contrôleur de caméra
      _controller = CameraController(
        _cameras!.first,
        ResolutionPreset.high,
        enableAudio: false,
      );

      await _controller!.initialize();

      if (!mounted) return;

      setState(() {
        _isInitializing = false;
      });

      // Annoncer que la fonctionnalité est prête
      await _ttsService.speak(
        'Reconnaissance d\'objets activée. Dirigez la caméra vers les objets à identifier.',
      );

      // Démarrer la détection (simulation pour l'instant)
      _startDetectionSimulation();
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur d\'initialisation: $e';
        _isInitializing = false;
      });
    }
  }

  /// Démarre la simulation de détection d'objets
  void _startDetectionSimulation() {
    // Simuler des détections d'objets toutes les 3-5 secondes
    Timer.periodic(const Duration(seconds: 4), (timer) {
      if (!mounted ||
          _controller == null ||
          !_controller!.value.isInitialized) {
        timer.cancel();
        return;
      }

      _simulateDetection();
    });
  }

  /// Simule la détection d'objets
  void _simulateDetection() {
    if (_isDetecting) return;

    _isDetecting = true;

    // Objets simulés avec leurs traductions
    final simulatedObjects = [
      {'class': 'person', 'confidence': 0.95},
      {'class': 'chair', 'confidence': 0.87},
      {'class': 'cell phone', 'confidence': 0.92},
      {'class': 'book', 'confidence': 0.78},
      {'class': 'cup', 'confidence': 0.85},
      {'class': 'laptop', 'confidence': 0.91},
      {'class': 'bottle', 'confidence': 0.83},
      {'class': 'car', 'confidence': 0.89},
    ];

    // Sélectionner 1-3 objets aléatoirement
    final random = math.Random();
    final numObjects = 1 + random.nextInt(3);
    final selectedObjects = <Map<String, dynamic>>[];

    for (int i = 0; i < numObjects; i++) {
      selectedObjects.add(
        simulatedObjects[random.nextInt(simulatedObjects.length)],
      );
    }

    setState(() {
      _recognitions = selectedObjects;
      _imageHeight = 480;
      _imageWidth = 640;
    });

    // Annoncer les objets détectés
    if (_voiceEnabled) {
      _announceDetectedObjects(selectedObjects);
    }

    _isDetecting = false;
  }

  /// Annonce vocalement les objets détectés
  Future<void> _announceDetectedObjects(
    List<Map<String, dynamic>> objects,
  ) async {
    final now = DateTime.now();

    // Vérifier le cooldown pour éviter trop d'annonces
    if (_lastAnnouncementTime != null &&
        now.difference(_lastAnnouncementTime!) < const Duration(seconds: 3)) {
      return;
    }

    _lastAnnouncementTime = now;

    // Filtrer les objets avec une confiance suffisante
    final significantObjects =
        objects
            .where((obj) => (obj['confidence'] as double) > 0.7)
            .take(3)
            .toList();

    if (significantObjects.isEmpty) return;

    String message;
    if (significantObjects.length == 1) {
      final obj = significantObjects.first;
      final translatedName = _translationService.translateToFrench(
        obj['class'],
      );
      final confidence = ((obj['confidence'] as double) * 100).toInt();
      message = 'Je vois $translatedName avec $confidence% de certitude';
    } else {
      final objectNames = significantObjects
          .map((obj) => _translationService.translateToFrench(obj['class']))
          .join(', ');
      message = 'Je vois plusieurs objets: $objectNames';
    }

    try {
      await _ttsService.speak(message);
    } catch (e) {
      debugPrint('Erreur lors de l\'annonce vocale: $e');
    }
  }

  /// Change le modèle de détection
  Future<void> _changeModel(String model) async {
    if (model != _selectedModel) {
      setState(() {
        _selectedModel = model;
      });
      HapticFeedback.lightImpact();
      await _ttsService.speak('Modèle $model sélectionné');
    }
  }

  /// Active/désactive les annonces vocales
  void _toggleVoiceAnnouncements() {
    setState(() {
      _voiceEnabled = !_voiceEnabled;
    });
    HapticFeedback.lightImpact();
    _ttsService.speak(
      _voiceEnabled
          ? 'Annonces vocales activées'
          : 'Annonces vocales désactivées',
    );
  }

  @override
  Widget build(BuildContext context) {
    final themeService = Provider.of<ThemeService>(context);
    final theme = themeService.getThemeData(context);

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text(
          'Reconnaissance d\'Objets',
          style: TextStyle(fontWeight: FontWeight.w600, fontSize: 20),
        ),
        backgroundColor: Colors.black.withValues(alpha: 0.8),
        elevation: 0,
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: const TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
        actions: [
          // Bouton de sélection de modèle
          PopupMenuButton<String>(
            icon: const Icon(Icons.model_training, color: Colors.white),
            onSelected: _changeModel,
            itemBuilder:
                (context) => [
                  PopupMenuItem(
                    value: ssd,
                    child: Row(
                      children: [
                        Icon(
                          _selectedModel == ssd
                              ? Icons.check
                              : Icons.circle_outlined,
                          color:
                              _selectedModel == ssd
                                  ? theme.colorScheme.primary
                                  : null,
                        ),
                        const SizedBox(width: 8),
                        const Text('SSD MobileNet'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: yolo,
                    child: Row(
                      children: [
                        Icon(
                          _selectedModel == yolo
                              ? Icons.check
                              : Icons.circle_outlined,
                          color:
                              _selectedModel == yolo
                                  ? theme.colorScheme.primary
                                  : null,
                        ),
                        const SizedBox(width: 8),
                        const Text('Tiny YOLOv2'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: mobilenet,
                    child: Row(
                      children: [
                        Icon(
                          _selectedModel == mobilenet
                              ? Icons.check
                              : Icons.circle_outlined,
                          color:
                              _selectedModel == mobilenet
                                  ? theme.colorScheme.primary
                                  : null,
                        ),
                        const SizedBox(width: 8),
                        const Text('MobileNet'),
                      ],
                    ),
                  ),
                ],
          ),
          // Bouton d'activation/désactivation des annonces vocales
          IconButton(
            icon: Icon(
              _voiceEnabled ? Icons.volume_up : Icons.volume_off,
              color: Colors.white,
            ),
            onPressed: _toggleVoiceAnnouncements,
          ),
        ],
      ),
      body: _buildBody(theme),
    );
  }

  /// Construit le corps de la page selon l'état
  Widget _buildBody(ThemeData theme) {
    if (_isInitializing) {
      return _buildLoadingView(theme);
    }

    if (_errorMessage != null) {
      return _buildErrorView(theme);
    }

    if (_cameras == null || _cameras!.isEmpty) {
      return _buildNoCameraView(theme);
    }

    return _buildCameraView(theme);
  }

  /// Vue de chargement
  Widget _buildLoadingView(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: theme.colorScheme.primary),
          const SizedBox(height: 16),
          Text(
            'Initialisation de la reconnaissance d\'objets...',
            style: theme.textTheme.bodyLarge?.copyWith(color: Colors.white),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Vue d'erreur
  Widget _buildErrorView(ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: theme.colorScheme.error),
            const SizedBox(height: 16),
            Text(
              'Erreur',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.error,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: theme.textTheme.bodyMedium?.copyWith(color: Colors.white),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _errorMessage = null;
                  _isInitializing = true;
                });
                _initializeCamera();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Réessayer'),
            ),
          ],
        ),
      ),
    );
  }

  /// Vue sans caméra
  Widget _buildNoCameraView(ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.camera_alt_outlined,
              size: 64,
              color: theme.colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              'Aucune caméra disponible',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Veuillez vérifier que votre appareil dispose d\'une caméra et que les permissions sont accordées.',
              style: theme.textTheme.bodyMedium?.copyWith(color: Colors.white),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Vue avec caméra active
  Widget _buildCameraView(ThemeData theme) {
    return Stack(
      children: [
        // Aperçu de la caméra
        if (_controller != null && _controller!.value.isInitialized)
          _buildCameraPreview(),

        // Overlay avec les résultats de détection
        if (_recognitions != null && _recognitions!.isNotEmpty)
          _buildDetectionOverlay(theme),

        // Panneau d'informations
        _buildInfoPanel(theme),
      ],
    );
  }

  /// Construit l'aperçu de la caméra
  Widget _buildCameraPreview() {
    final size = MediaQuery.of(context).size;
    final deviceRatio = size.width / size.height;

    return ClipRect(
      child: Transform.scale(
        scale: _controller!.value.aspectRatio / deviceRatio,
        child: Center(
          child: AspectRatio(
            aspectRatio: _controller!.value.aspectRatio,
            child: CameraPreview(_controller!),
          ),
        ),
      ),
    );
  }

  /// Construit l'overlay de détection
  Widget _buildDetectionOverlay(ThemeData theme) {
    return Positioned.fill(
      child: CustomPaint(
        painter: DetectionPainter(
          recognitions: _recognitions!,
          imageHeight: _imageHeight,
          imageWidth: _imageWidth,
          screenHeight: MediaQuery.of(context).size.height,
          screenWidth: MediaQuery.of(context).size.width,
          primaryColor: theme.colorScheme.primary,
        ),
      ),
    );
  }

  /// Construit le panneau d'informations
  Widget _buildInfoPanel(ThemeData theme) {
    return Positioned(
      top: 80,
      right: 16,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 200),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.8),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: theme.colorScheme.primary.withValues(alpha: 0.3),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // En-tête
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _selectedModel,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
                Icon(
                  _voiceEnabled ? Icons.volume_up : Icons.volume_off,
                  size: 16,
                  color:
                      _voiceEnabled ? theme.colorScheme.primary : Colors.grey,
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Résultats de détection
            if (_recognitions == null || _recognitions!.isEmpty)
              const Text(
                'Recherche d\'objets...',
                style: TextStyle(color: Colors.white70, fontSize: 11),
              )
            else
              ..._recognitions!.take(5).map((recognition) {
                final className = recognition['class'] as String;
                final confidence = recognition['confidence'] as double;
                final translatedName = _translationService.translateToFrench(
                  className,
                );

                return Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      Container(
                        width: 6,
                        height: 6,
                        decoration: BoxDecoration(
                          color: _getConfidenceColor(confidence, theme),
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Text(
                          '$translatedName (${(confidence * 100).toInt()}%)',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                );
              }),
          ],
        ),
      ),
    );
  }

  /// Retourne une couleur basée sur le niveau de confiance
  Color _getConfidenceColor(double confidence, ThemeData theme) {
    if (confidence > 0.8) return Colors.green;
    if (confidence > 0.6) return Colors.orange;
    if (confidence > 0.4) return theme.colorScheme.primary;
    return Colors.grey;
  }
}

/// Painter pour dessiner les boîtes de détection
class DetectionPainter extends CustomPainter {
  final List<dynamic> recognitions;
  final int imageHeight;
  final int imageWidth;
  final double screenHeight;
  final double screenWidth;
  final Color primaryColor;

  DetectionPainter({
    required this.recognitions,
    required this.imageHeight,
    required this.imageWidth,
    required this.screenHeight,
    required this.screenWidth,
    required this.primaryColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = primaryColor
          ..strokeWidth = 3.0
          ..style = PaintingStyle.stroke;

    final textPaint = Paint()..color = primaryColor;

    for (final recognition in recognitions) {
      // Pour la simulation, on dessine juste des boîtes aléatoirement positionnées
      final random = math.Random(recognition['class'].hashCode);
      final left = random.nextDouble() * (screenWidth - 100);
      final top = random.nextDouble() * (screenHeight - 100);
      final width = 80 + random.nextDouble() * 120;
      final height = 60 + random.nextDouble() * 80;

      // Dessiner la boîte
      final rect = Rect.fromLTWH(left, top, width, height);
      canvas.drawRect(rect, paint);

      // Dessiner le label
      final className = recognition['class'] as String;
      final confidence = recognition['confidence'] as double;
      final label = '$className ${(confidence * 100).toInt()}%';

      final textSpan = TextSpan(
        text: label,
        style: TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
          backgroundColor: primaryColor,
        ),
      );

      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();

      // Dessiner le fond du texte
      final textRect = Rect.fromLTWH(left, top - 20, textPainter.width + 8, 20);
      canvas.drawRect(textRect, textPaint);

      // Dessiner le texte
      textPainter.paint(canvas, Offset(left + 4, top - 18));
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

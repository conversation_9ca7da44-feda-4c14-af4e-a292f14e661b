import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:camera/camera.dart';
import 'package:voice_assistant/services/tts_service.dart';
import 'package:voice_assistant/services/translation_service.dart';
import 'package:voice_assistant/models/detection_models.dart';

/// Service de détection d'objets en temps réel avec TensorFlow Lite
class RealObjectDetectionService extends ChangeNotifier {
  static RealObjectDetectionService? _instance;

  static RealObjectDetectionService get instance {
    _instance ??= RealObjectDetectionService._internal();
    return _instance!;
  }

  // Services
  final TtsService _ttsService;
  final TranslationService _translationService;

  // État du service
  String _currentModel = ssd;
  List<dynamic>? _recognitions;
  bool _isDetecting = false;
  bool _voiceEnabled = true;
  DateTime? _lastAnnouncementTime;
  bool _isModelLoaded = false;

  // Paramètres
  static const Duration _announcementCooldown = Duration(seconds: 3);
  static const double _confidenceThreshold = 0.4;

  /// Constructeur interne
  RealObjectDetectionService._internal({
    TtsService? ttsService,
    TranslationService? translationService,
  }) : _ttsService = ttsService ?? TtsService(),
       _translationService = translationService ?? TranslationService();

  /// Factory constructor par défaut
  factory RealObjectDetectionService() => instance;

  /// Getters
  String get currentModel => _currentModel;
  List<dynamic>? get recognitions => _recognitions;
  bool get isDetecting => _isDetecting;
  bool get voiceEnabled => _voiceEnabled;
  bool get isModelLoaded => _isModelLoaded;

  /// Active/désactive les annonces vocales
  void setVoiceEnabled(bool enabled) {
    _voiceEnabled = enabled;
    notifyListeners();
  }

  /// Initialise le service
  Future<void> initialize() async {
    try {
      await _loadModel(_currentModel);
      debugPrint('Service de détection d\'objets initialisé avec succès');
    } catch (e) {
      debugPrint('Erreur d\'initialisation du service: $e');
      rethrow;
    }
  }

  /// Charge un modèle TensorFlow Lite
  Future<void> _loadModel(String model) async {
    try {
      _isModelLoaded = false;
      notifyListeners();

      String? result;
      switch (model) {
        case yolo:
          result = await Tflite.loadModel(
            model: "assets/models/yolov2_tiny.tflite",
            labels: "assets/models/yolov2_tiny.txt",
          );
          break;
        case mobilenet:
          result = await Tflite.loadModel(
            model: "assets/models/mobilenet_v1_1.0_224.tflite",
            labels: "assets/models/mobilenet_v1_1.0_224.txt",
          );
          break;
        case posenet:
          result = await Tflite.loadModel(
            model:
                "assets/models/posenet_mv1_075_float_from_checkpoints.tflite",
          );
          break;
        default: // ssd
          result = await Tflite.loadModel(
            model: "assets/models/ssd_mobilenet.tflite",
            labels: "assets/models/ssd_mobilenet.txt",
          );
      }

      if (result != null) {
        _isModelLoaded = true;
        _currentModel = model;
        debugPrint('Modèle $model chargé avec succès: $result');
      } else {
        throw Exception('Échec du chargement du modèle $model');
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors du chargement du modèle: $e');
      rethrow;
    }
  }

  /// Change le modèle de détection
  Future<void> changeModel(String model) async {
    if (_currentModel == model) return;

    try {
      await _loadModel(model);
      if (_voiceEnabled) {
        await _ttsService.speak('Modèle $model activé');
      }
    } catch (e) {
      debugPrint('Erreur lors du changement de modèle: $e');
      if (_voiceEnabled) {
        await _ttsService.speak('Erreur lors du changement de modèle');
      }
    }
  }

  /// Détecte les objets dans une image de la caméra
  Future<void> detectObjects(CameraImage image) async {
    if (!_isModelLoaded || _isDetecting) return;

    _isDetecting = true;

    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;
      List<dynamic>? recognitions;

      if (_currentModel == mobilenet) {
        recognitions = await Tflite.runModelOnFrame(
          bytesList: image.planes.map((plane) => plane.bytes).toList(),
          imageHeight: image.height,
          imageWidth: image.width,
          numResults: 2,
        );
      } else if (_currentModel == posenet) {
        recognitions = await Tflite.runPoseNetOnFrame(
          bytesList: image.planes.map((plane) => plane.bytes).toList(),
          imageHeight: image.height,
          imageWidth: image.width,
          numResults: 2,
        );
      } else {
        // YOLO ou SSD MobileNet
        recognitions = await Tflite.detectObjectOnFrame(
          bytesList: image.planes.map((plane) => plane.bytes).toList(),
          model: _currentModel == yolo ? "YOLO" : "SSDMobileNet",
          imageHeight: image.height,
          imageWidth: image.width,
          imageMean: _currentModel == yolo ? 0 : 127.5,
          imageStd: _currentModel == yolo ? 255.0 : 127.5,
          numResultsPerClass: 1,
          threshold: _currentModel == yolo ? 0.2 : 0.4,
        );
      }

      final endTime = DateTime.now().millisecondsSinceEpoch;
      debugPrint("Détection terminée en ${endTime - startTime}ms");

      _recognitions = recognitions;
      notifyListeners();

      // Annoncer les objets détectés
      if (_voiceEnabled && recognitions != null && recognitions.isNotEmpty) {
        await _announceDetectedObjects(recognitions);
      }
    } catch (e) {
      debugPrint('Erreur lors de la détection: $e');
    } finally {
      _isDetecting = false;
    }
  }

  /// Annonce vocalement les objets détectés
  Future<void> _announceDetectedObjects(List<dynamic> recognitions) async {
    final now = DateTime.now();

    // Vérifier le cooldown
    if (_lastAnnouncementTime != null &&
        now.difference(_lastAnnouncementTime!) < _announcementCooldown) {
      return;
    }

    _lastAnnouncementTime = now;

    // Filtrer les résultats significatifs
    final significantResults =
        recognitions
            .where((result) {
              final confidence = _getConfidence(result);
              return confidence > _confidenceThreshold;
            })
            .take(3)
            .toList();

    if (significantResults.isEmpty) return;

    String message;
    if (significantResults.length == 1) {
      final result = significantResults.first;
      final className = _getClassName(result);
      final confidence = _getConfidence(result);
      final translatedName = _translationService.translateToFrench(className);
      message =
          'Je vois $translatedName avec ${(confidence * 100).toInt()}% de certitude';
    } else {
      final objectNames = significantResults
          .map(
            (result) =>
                _translationService.translateToFrench(_getClassName(result)),
          )
          .join(', ');
      message = 'Je vois plusieurs objets: $objectNames';
    }

    try {
      await _ttsService.speak(message);
    } catch (e) {
      debugPrint('Erreur lors de l\'annonce vocale: $e');
    }
  }

  /// Extrait le nom de classe d'un résultat de détection
  String _getClassName(dynamic result) {
    if (result is Map) {
      return result['detectedClass'] ?? result['label'] ?? 'objet inconnu';
    }
    return 'objet inconnu';
  }

  /// Extrait la confiance d'un résultat de détection
  double _getConfidence(dynamic result) {
    if (result is Map) {
      return (result['confidenceInClass'] ?? result['confidence'] ?? 0.0)
          as double;
    }
    return 0.0;
  }

  /// Nettoie les ressources
  @override
  void dispose() {
    Tflite.close();
    super.dispose();
  }

  /// Réinitialise l'instance (pour les tests)
  @visibleForTesting
  static void resetInstance() {
    _instance?.dispose();
    _instance = null;
  }
}

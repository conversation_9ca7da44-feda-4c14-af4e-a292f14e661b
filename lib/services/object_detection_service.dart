import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:camera/camera.dart';
import 'package:tflite/tflite.dart';
import 'package:voice_assistant/services/tts_service.dart';
import 'package:voice_assistant/services/translation_service.dart';
import 'package:voice_assistant/models/detection_models.dart';

/// Types de modèles de détection disponibles
enum DetectionModel {
  ssdMobileNet(
    'SSD MobileNet',
    'assets/models/ssd_mobilenet.tflite',
    'assets/models/ssd_mobilenet.txt',
  ),
  yoloTiny(
    'Tiny YOLOv2',
    'assets/models/yolov2_tiny.tflite',
    'assets/models/yolov2_tiny.txt',
  ),
  mobileNet(
    'MobileNet',
    'assets/models/mobilenet_v1_1.0_224.tflite',
    'assets/models/mobilenet_v1_1.0_224.txt',
  ),
  poseNet(
    'PoseNet',
    'assets/models/posenet_mv1_075_float_from_checkpoints.tflite',
    null,
  );

  const DetectionModel(this.displayName, this.modelPath, this.labelsPath);

  final String displayName;
  final String modelPath;
  final String? labelsPath;
}

/// États du service de détection
enum DetectionState { idle, loading, detecting, error }

/// Résultat de détection d'objet
class DetectionResult {
  final String detectedClass;
  final double confidence;
  final Map<String, double>? rect; // Pour les boîtes de détection
  final Map<String, dynamic>? keypoints; // Pour PoseNet
  final String? label; // Pour MobileNet

  DetectionResult({
    required this.detectedClass,
    required this.confidence,
    this.rect,
    this.keypoints,
    this.label,
  });

  @override
  String toString() {
    return 'DetectionResult(class: $detectedClass, confidence: ${(confidence * 100).toStringAsFixed(1)}%)';
  }
}

/// Service de détection d'objets en temps réel
/// Utilise TensorFlow Lite pour détecter des objets dans les images de la caméra
class ObjectDetectionService extends ChangeNotifier {
  static ObjectDetectionService? _instance;

  static ObjectDetectionService get instance {
    _instance ??= ObjectDetectionService._internal();
    return _instance!;
  }

  // Services et dépendances
  final TtsService _ttsService;
  final TranslationService _translationService;
  Interpreter? _interpreter;
  List<String>? _labels;

  // État du service
  DetectionState _state = DetectionState.idle;
  DetectionModel _currentModel = DetectionModel.ssdMobileNet;
  List<DetectionResult> _lastResults = [];
  String? _errorMessage;
  bool _isDetecting = false;
  bool _voiceAnnouncementEnabled = true;

  // Paramètres de détection
  static const int _maxResults = 5;
  static const double _confidenceThreshold = 0.4;
  static const Duration _announcementCooldown = Duration(seconds: 3);
  DateTime? _lastAnnouncementTime;

  /// Constructeur interne
  ObjectDetectionService._internal({
    TtsService? ttsService,
    TranslationService? translationService,
  }) : _ttsService = ttsService ?? TtsService(),
       _translationService = translationService ?? TranslationService();

  /// Factory constructor par défaut
  factory ObjectDetectionService() => instance;

  /// Getters
  DetectionState get state => _state;
  DetectionModel get currentModel => _currentModel;
  List<DetectionResult> get lastResults => List.unmodifiable(_lastResults);
  String? get errorMessage => _errorMessage;
  bool get isDetecting => _isDetecting;
  bool get voiceAnnouncementEnabled => _voiceAnnouncementEnabled;

  /// Active/désactive les annonces vocales
  void setVoiceAnnouncementEnabled(bool enabled) {
    _voiceAnnouncementEnabled = enabled;
    notifyListeners();
  }

  /// Change le modèle de détection
  Future<void> changeModel(DetectionModel model) async {
    if (_currentModel == model) return;

    _currentModel = model;
    await _loadModel();
  }

  /// Initialise le service
  Future<void> initialize() async {
    if (_state == DetectionState.loading) return;

    _setState(DetectionState.loading);

    try {
      await _loadModel();
      _setState(DetectionState.idle);
      debugPrint('Service de détection d\'objets initialisé avec succès');
    } catch (e) {
      _setError('Erreur d\'initialisation: $e');
    }
  }

  /// Charge le modèle TensorFlow Lite
  Future<void> _loadModel() async {
    try {
      // Libérer l'ancien modèle
      _interpreter?.close();
      _interpreter = null;
      _labels = null;

      // Charger le nouveau modèle
      _interpreter = await Interpreter.fromAsset(_currentModel.modelPath);

      // Charger les labels si disponibles
      if (_currentModel.labelsPath != null) {
        final labelsData = await rootBundle.loadString(
          _currentModel.labelsPath!,
        );
        _labels =
            labelsData
                .split('\n')
                .where((line) => line.trim().isNotEmpty)
                .toList();
      }

      debugPrint('Modèle ${_currentModel.displayName} chargé avec succès');
      debugPrint('Nombre de labels: ${_labels?.length ?? 0}');
    } catch (e) {
      throw Exception('Erreur lors du chargement du modèle: $e');
    }
  }

  /// Traite une image de la caméra pour détecter des objets
  Future<void> detectObjects(CameraImage image) async {
    if (_interpreter == null || _isDetecting || _state != DetectionState.idle) {
      return;
    }

    _isDetecting = true;
    _setState(DetectionState.detecting);

    try {
      final startTime = DateTime.now().millisecondsSinceEpoch;

      // Convertir l'image de la caméra en format utilisable
      final inputData = _preprocessImage(image);

      // Préparer les tensors de sortie
      final outputData = _prepareOutputTensors();

      // Exécuter l'inférence
      _interpreter!.runForMultipleInputs([inputData], outputData);

      // Traiter les résultats
      final results = _processResults(outputData, image.width, image.height);

      final endTime = DateTime.now().millisecondsSinceEpoch;
      debugPrint('Détection terminée en ${endTime - startTime}ms');

      _updateResults(results);
    } catch (e) {
      _setError('Erreur de détection: $e');
    } finally {
      _isDetecting = false;
      if (_state == DetectionState.detecting) {
        _setState(DetectionState.idle);
      }
    }
  }

  /// Prétraite l'image de la caméra
  List<List<List<List<double>>>> _preprocessImage(CameraImage image) {
    // Cette méthode sera implémentée selon le modèle utilisé
    // Pour l'instant, retourner une structure basique
    const int inputSize = 224; // Taille d'entrée standard

    // Créer un tensor d'entrée de base (sera amélioré)
    return List.generate(
      1,
      (i) => List.generate(
        inputSize,
        (j) => List.generate(inputSize, (k) => List.generate(3, (l) => 0.0)),
      ),
    );
  }

  /// Prépare les tensors de sortie
  Map<int, Object> _prepareOutputTensors() {
    // Structure de base pour les tensors de sortie
    return {
      0: List.filled(_maxResults * 4, 0.0), // Boîtes de détection
      1: List.filled(_maxResults, 0.0), // Classes
      2: List.filled(_maxResults, 0.0), // Scores
      3: [0.0], // Nombre de détections
    };
  }

  /// Traite les résultats de l'inférence
  List<DetectionResult> _processResults(
    Map<int, Object> outputs,
    int imageWidth,
    int imageHeight,
  ) {
    final results = <DetectionResult>[];

    // Cette méthode sera implémentée selon le format de sortie du modèle
    // Pour l'instant, retourner une liste vide

    return results;
  }

  /// Met à jour les résultats et déclenche les annonces vocales
  void _updateResults(List<DetectionResult> results) {
    _lastResults = results;
    notifyListeners();

    // Annoncer les objets détectés si activé
    if (_voiceAnnouncementEnabled && results.isNotEmpty) {
      _announceDetectedObjects(results);
    }
  }

  /// Annonce vocalement les objets détectés
  Future<void> _announceDetectedObjects(List<DetectionResult> results) async {
    final now = DateTime.now();

    // Vérifier le cooldown pour éviter trop d'annonces
    if (_lastAnnouncementTime != null &&
        now.difference(_lastAnnouncementTime!) < _announcementCooldown) {
      return;
    }

    _lastAnnouncementTime = now;

    // Filtrer les résultats avec une confiance suffisante
    final significantResults =
        results
            .where((result) => result.confidence > _confidenceThreshold)
            .take(3) // Limiter à 3 objets pour éviter la surcharge
            .toList();

    if (significantResults.isEmpty) return;

    // Construire le message d'annonce
    String message;
    if (significantResults.length == 1) {
      final result = significantResults.first;
      final translatedName = _translateToFrench(result.detectedClass);
      message =
          'Je vois $translatedName avec ${(result.confidence * 100).toInt()}% de certitude';
    } else {
      final objectNames = significantResults
          .map((result) => _translateToFrench(result.detectedClass))
          .join(', ');
      message = 'Je vois plusieurs objets: $objectNames';
    }

    // Annoncer le message
    try {
      await _ttsService.speak(message);
    } catch (e) {
      debugPrint('Erreur lors de l\'annonce vocale: $e');
    }
  }

  /// Traduit les noms d'objets en français
  String _translateToFrench(String englishName) {
    return _translationService.translateToFrench(englishName);
  }

  /// Met à jour l'état du service
  void _setState(DetectionState newState) {
    if (_state != newState) {
      _state = newState;
      notifyListeners();
    }
  }

  /// Définit une erreur
  void _setError(String error) {
    _errorMessage = error;
    _setState(DetectionState.error);
    debugPrint('Erreur ObjectDetectionService: $error');
  }

  /// Nettoie les ressources
  @override
  void dispose() {
    _interpreter?.close();
    super.dispose();
  }

  /// Réinitialise l'instance (pour les tests)
  @visibleForTesting
  static void resetInstance() {
    _instance?.dispose();
    _instance = null;
  }
}

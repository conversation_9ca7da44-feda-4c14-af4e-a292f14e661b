import 'package:flutter/foundation.dart';

/// Service de traduction pour les objets détectés
/// Traduit les noms d'objets de l'anglais vers le français
class TranslationService {
  static TranslationService? _instance;

  static TranslationService get instance {
    _instance ??= TranslationService._internal();
    return _instance!;
  }

  TranslationService._internal();

  factory TranslationService() => instance;

  /// Dictionnaire de traduction complet pour les objets COCO
  static const Map<String, String> _translations = {
    // Personnes et animaux
    'person': 'une personne',
    'people': 'des personnes',
    'bird': 'un oiseau',
    'cat': 'un chat',
    'dog': 'un chien',
    'horse': 'un cheval',
    'sheep': 'un mouton',
    'cow': 'une vache',
    'elephant': 'un éléphant',
    'bear': 'un ours',
    'zebra': 'un zèbre',
    'giraffe': 'une girafe',

    // Véhicules
    'bicycle': 'un vélo',
    'car': 'une voiture',
    'motorcycle': 'une moto',
    'airplane': 'un avion',
    'bus': 'un bus',
    'train': 'un train',
    'truck': 'un camion',
    'boat': 'un bateau',

    // Signalisation et mobilier urbain
    'traffic light': 'un feu de circulation',
    'fire hydrant': 'une bouche d\'incendie',
    'stop sign': 'un panneau stop',
    'parking meter': 'un parcmètre',
    'bench': 'un banc',

    // Accessoires et bagages
    'backpack': 'un sac à dos',
    'umbrella': 'un parapluie',
    'handbag': 'un sac à main',
    'tie': 'une cravate',
    'suitcase': 'une valise',

    // Sports et loisirs
    'frisbee': 'un frisbee',
    'skis': 'des skis',
    'snowboard': 'un snowboard',
    'sports ball': 'un ballon',
    'kite': 'un cerf-volant',
    'baseball bat': 'une batte de baseball',
    'baseball glove': 'un gant de baseball',
    'skateboard': 'un skateboard',
    'surfboard': 'une planche de surf',
    'tennis racket': 'une raquette de tennis',

    // Cuisine et alimentation
    'bottle': 'une bouteille',
    'wine glass': 'un verre à vin',
    'cup': 'une tasse',
    'fork': 'une fourchette',
    'knife': 'un couteau',
    'spoon': 'une cuillère',
    'bowl': 'un bol',
    'banana': 'une banane',
    'apple': 'une pomme',
    'sandwich': 'un sandwich',
    'orange': 'une orange',
    'broccoli': 'du brocoli',
    'carrot': 'une carotte',
    'hot dog': 'un hot-dog',
    'pizza': 'une pizza',
    'donut': 'un donut',
    'cake': 'un gâteau',

    // Mobilier
    'chair': 'une chaise',
    'couch': 'un canapé',
    'potted plant': 'une plante en pot',
    'bed': 'un lit',
    'dining table': 'une table à manger',
    'toilet': 'des toilettes',

    // Électronique
    'tv': 'une télévision',
    'laptop': 'un ordinateur portable',
    'mouse': 'une souris d\'ordinateur',
    'remote': 'une télécommande',
    'keyboard': 'un clavier',
    'cell phone': 'un téléphone portable',
    'microwave': 'un micro-ondes',
    'oven': 'un four',
    'toaster': 'un grille-pain',
    'sink': 'un évier',
    'refrigerator': 'un réfrigérateur',

    // Objets divers
    'book': 'un livre',
    'clock': 'une horloge',
    'vase': 'un vase',
    'scissors': 'des ciseaux',
    'teddy bear': 'un ours en peluche',
    'hair drier': 'un sèche-cheveux',
    'toothbrush': 'une brosse à dents',

    // Objets supplémentaires courants
    'door': 'une porte',
    'window': 'une fenêtre',
    'wall': 'un mur',
    'floor': 'le sol',
    'ceiling': 'le plafond',
    'light': 'une lumière',
    'lamp': 'une lampe',
    'mirror': 'un miroir',
    'picture': 'une image',
    'painting': 'un tableau',
    'plant': 'une plante',
    'flower': 'une fleur',
    'tree': 'un arbre',
    'grass': 'de l\'herbe',
    'sky': 'le ciel',
    'cloud': 'un nuage',
    'sun': 'le soleil',
    'moon': 'la lune',
    'star': 'une étoile',
    'water': 'de l\'eau',
    'fire': 'du feu',
    'smoke': 'de la fumée',
    'snow': 'de la neige',
    'rain': 'de la pluie',
    'wind': 'du vent',
    'stone': 'une pierre',
    'rock': 'un rocher',
    'sand': 'du sable',
    'dirt': 'de la terre',
    'mud': 'de la boue',
    'ice': 'de la glace',
    'glass': 'du verre',
    'metal': 'du métal',
    'wood': 'du bois',
    'plastic': 'du plastique',
    'paper': 'du papier',
    'cloth': 'du tissu',
    'leather': 'du cuir',
    'rubber': 'du caoutchouc',
    'food': 'de la nourriture',
    'drink': 'une boisson',
    'medicine': 'un médicament',
    'tool': 'un outil',
    'weapon': 'une arme',
    'toy': 'un jouet',
    'game': 'un jeu',
    'instrument': 'un instrument',
    'machine': 'une machine',
    'vehicle': 'un véhicule',
    'building': 'un bâtiment',
    'house': 'une maison',
    'road': 'une route',
    'bridge': 'un pont',
    'mountain': 'une montagne',
    'river': 'une rivière',
    'lake': 'un lac',
    'ocean': 'un océan',
    'beach': 'une plage',
    'forest': 'une forêt',
    'field': 'un champ',
    'garden': 'un jardin',
    'park': 'un parc',
    'street': 'une rue',
    'sidewalk': 'un trottoir',
    'stairs': 'des escaliers',
    'elevator': 'un ascenseur',
    'escalator': 'un escalator',
    'fence': 'une clôture',
    'gate': 'un portail',
    'sign': 'un panneau',
    'flag': 'un drapeau',
    'banner': 'une bannière',
    'poster': 'une affiche',
    'advertisement': 'une publicité',
    'logo': 'un logo',
    'symbol': 'un symbole',
    'number': 'un numéro',
    'letter': 'une lettre',
    'word': 'un mot',
    'text': 'du texte',
    'message': 'un message',
    'note': 'une note',
    'list': 'une liste',
    'menu': 'un menu',
    'recipe': 'une recette',
    'map': 'une carte',
    'calendar': 'un calendrier',
    'schedule': 'un horaire',
    'ticket': 'un ticket',
    'card': 'une carte',
    'money': 'de l\'argent',
    'coin': 'une pièce',
    'bill': 'un billet',
    'wallet': 'un portefeuille',
    'purse': 'un porte-monnaie',
    'bag': 'un sac',
    'box': 'une boîte',
    'container': 'un conteneur',
    'package': 'un paquet',
    'gift': 'un cadeau',
    'present': 'un présent',
    'decoration': 'une décoration',
    'ornament': 'un ornement',
    'jewelry': 'des bijoux',
    'ring': 'une bague',
    'necklace': 'un collier',
    'bracelet': 'un bracelet',
    'watch': 'une montre',
    'glasses': 'des lunettes',
    'sunglasses': 'des lunettes de soleil',
    'hat': 'un chapeau',
    'cap': 'une casquette',
    'helmet': 'un casque',
    'mask': 'un masque',
    'glove': 'un gant',
    'shoe': 'une chaussure',
    'boot': 'une botte',
    'sock': 'une chaussette',
    'shirt': 'une chemise',
    'pants': 'un pantalon',
    'dress': 'une robe',
    'skirt': 'une jupe',
    'coat': 'un manteau',
    'jacket': 'une veste',
    'sweater': 'un pull',
    'scarf': 'une écharpe',
    'belt': 'une ceinture',
    'button': 'un bouton',
    'zipper': 'une fermeture éclair',
    'pocket': 'une poche',
    'sleeve': 'une manche',
    'collar': 'un col',
    'hood': 'une capuche',
  };

  /// Traduit un nom d'objet de l'anglais vers le français
  String translateToFrench(String englishName) {
    final normalizedName = englishName.toLowerCase().trim();
    return _translations[normalizedName] ?? englishName;
  }

  /// Traduit une liste d'objets et retourne une phrase descriptive
  String translateObjectList(List<String> objects) {
    if (objects.isEmpty) return 'Aucun objet détecté';

    final translatedObjects = objects.map(translateToFrench).toList();

    if (translatedObjects.length == 1) {
      return 'Je vois ${translatedObjects.first}';
    } else if (translatedObjects.length == 2) {
      return 'Je vois ${translatedObjects[0]} et ${translatedObjects[1]}';
    } else {
      final lastObject = translatedObjects.removeLast();
      return 'Je vois ${translatedObjects.join(', ')} et $lastObject';
    }
  }

  /// Génère une phrase d'annonce avec les niveaux de confiance
  String generateAnnouncementWithConfidence(
    Map<String, double> objectsWithConfidence,
  ) {
    if (objectsWithConfidence.isEmpty) return 'Aucun objet détecté';

    final entries = objectsWithConfidence.entries.toList();

    if (entries.length == 1) {
      final entry = entries.first;
      final translatedName = translateToFrench(entry.key);
      final confidence = (entry.value * 100).toInt();
      return 'Je vois $translatedName avec $confidence% de certitude';
    } else {
      final objectNames = entries
          .map((entry) => translateToFrench(entry.key))
          .join(', ');
      return 'Je vois plusieurs objets: $objectNames';
    }
  }

  /// Vérifie si une traduction existe pour un objet donné
  bool hasTranslation(String englishName) {
    return _translations.containsKey(englishName.toLowerCase().trim());
  }

  /// Retourne toutes les traductions disponibles
  Map<String, String> getAllTranslations() {
    return Map.unmodifiable(_translations);
  }

  /// Ajoute ou met à jour une traduction (pour les extensions futures)
  void addTranslation(String englishName, String frenchName) {
    // Note: Cette méthode pourrait être utilisée pour des traductions personnalisées
    // Pour l'instant, on utilise un dictionnaire statique
    debugPrint('Traduction personnalisée ajoutée: $englishName -> $frenchName');
  }

  /// Réinitialise l'instance (pour les tests)
  @visibleForTesting
  static void resetInstance() {
    _instance = null;
  }
}

name: voice_assistant
description: "A flutter assistant application based on stt and tts on flutter with firebase"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  firebase_core: ^2.32.0
  firebase_auth: ^4.16.0
  google_sign_in: ^6.3.0
  speech_to_text: ^7.0.0
  permission_handler: ^12.0.0+1
  lottie: ^3.3.1
  google_fonts: ^6.2.1
  shared_preferences: ^2.2.2
  provider: ^6.0.5
  android_intent_plus: ^5.3.0
  cloud_firestore: ^4.17.5
  uuid: ^3.0.7
  intl: ^0.20.2
  timezone: ^0.9.2
  just_audio: ^0.10.0
  flutter_tts: ^3.8.5
  audio_session: ^0.1.18
  audio_service: ^0.18.18
  http: ^1.1.0
  firebase_app_check: ^0.2.2
  local_auth: ^2.1.7
  path_provider: ^2.1.2

  # OpenStreetMap avec Flutter Map
  # Alternative gratuite et complète à Google Maps
  flutter_map: ^6.1.0
  latlong2: ^0.9.0
  location: ^5.0.0  # Alternative à geolocator
  cached_network_image: ^3.3.1
  awesome_notifications: ^0.10.1
  awesome_notifications_core: ^0.10.1

  # Reconnaissance d'objets avec caméra
  camera: ^0.10.5+9

  # TensorFlow Lite pour la détection d'objets
  tflite:
    path: packages/tflite

  # Analyse d'images pour détection d'objets
  image: ^4.1.7

  # Chat avec Gemini AI (gratuit)
  google_generative_ai: ^0.4.3

  # Image picker pour les tests
  image_picker: ^1.0.4

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  mockito: ^5.4.4
  build_runner: ^2.4.8
  fake_async: ^1.3.1
  test: ^1.24.9

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/google.png
    # Modèles TensorFlow Lite pour la détection d'objets
    - assets/models/yolov2_tiny.tflite
    - assets/models/yolov2_tiny.txt
    - assets/models/ssd_mobilenet.tflite
    - assets/models/ssd_mobilenet.txt
    - assets/models/mobilenet_v1_1.0_224.tflite
    - assets/models/mobilenet_v1_1.0_224.txt
    - assets/models/posenet_mv1_075_float_from_checkpoints.tflite
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
